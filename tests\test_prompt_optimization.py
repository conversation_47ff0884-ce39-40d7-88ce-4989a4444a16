"""
测试PromptOptimizationAgent的示例脚本

演示如何使用新的4个核心LLM驱动工具来优化用户提示词。
包含简单和复杂的测试场景。
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.iicrawlermcp.agents.prompt_agent import build_prompt_optimization_agent
from src.iicrawlermcp.core.config import config


def test_simple_prompt_optimization():
    """
    测试简单提示词优化
    
    场景：用户输入非常简短模糊的提示词
    """
    print("\n" + "="*60)
    print("测试1：简单提示词优化")
    print("="*60)
    
    # 创建PromptOptimizationAgent
    agent = build_prompt_optimization_agent(verbose=True)
    
    # 测试用例：非常简短的提示词
    simple_prompts = [
        "爬淘宝数据",
        "搜索京东",
        "获取网页信息",
        "booking订酒店"
    ]
    
    for prompt in simple_prompts:
        print(f"\n原始输入: {prompt}")
        print("-" * 40)
        
        # 调用Agent优化提示词
        result = agent.invoke(prompt)
        
        print(f"优化结果:\n{result.get('output', '无结果')}")
        print("=" * 60)
        
        # 只测试第一个，避免过多API调用
        break


def test_complex_prompt_optimization():
    """
    测试复杂提示词优化
    
    场景：用户输入包含多个步骤但结构混乱的提示词
    """
    print("\n" + "="*60)
    print("测试2：复杂提示词优化")
    print("="*60)
    
    agent = build_prompt_optimization_agent(verbose=True)
    
    complex_prompt = """
    我想要爬取电商网站的数据，主要是商品信息，
    需要包括价格和评价，最好能保存下来，
    可能需要处理分页，还要避免被封
    """
    
    print(f"原始输入: {complex_prompt}")
    print("-" * 40)
    
    # 执行优化
    result = agent.invoke(complex_prompt)
    
    print(f"优化结果:\n{result.get('output', '无结果')}")


def test_with_specific_website():
    """
    测试带有具体网站的提示词优化
    
    场景：用户提到具体网站但没有提供域名
    """
    print("\n" + "="*60)
    print("测试3：带具体网站的提示词优化")
    print("="*60)
    
    agent = build_prompt_optimization_agent(verbose=True)
    
    website_prompt = "在携程上搜索上海的酒店，找五星级的"
    
    print(f"原始输入: {website_prompt}")
    print("-" * 40)
    
    # 执行优化 - Agent会使用search_web_context查找携程的域名
    result = agent.invoke(website_prompt)
    
    print(f"优化结果:\n{result.get('output', '无结果')}")


def test_interactive_optimization():
    """
    测试交互式优化流程
    
    演示完整的4步优化流程：
    1. 生成标准模板
    2. 搜索网络信息
    3. 生成澄清问题
    4. 输出优化结果
    """
    print("\n" + "="*60)
    print("测试4：交互式优化流程")
    print("="*60)
    
    agent = build_prompt_optimization_agent(verbose=True)
    
    # 用户输入
    user_input = "爬取招聘网站的Python岗位"
    
    print(f"用户输入: {user_input}")
    print("-" * 40)
    
    # 执行优化流程
    # Agent内部会：
    # 1. 调用generate_standard_template生成模板
    # 2. 调用search_web_context搜索"招聘网站"、"Python岗位爬取"等
    # 3. 调用generate_clarifying_questions生成问题
    # 4. 调用generate_optimized_prompt生成最终结果
    
    result = agent.invoke(user_input)
    
    print(f"优化流程输出:\n{result.get('output', '无结果')}")
    
    # 如果有澄清问题，模拟用户回答
    if "请回答以下问题" in str(result.get('output', '')):
        print("\n模拟用户回答澄清问题...")
        user_answers = """
        1. 我想爬取BOSS直聘
        2. 需要获取100个岗位
        3. 主要关注上海地区
        """
        
        # 再次调用，提供补充信息
        followup_prompt = f"{user_input}\n\n补充信息：{user_answers}"
        result = agent.invoke(followup_prompt)
        
        print(f"最终优化结果:\n{result.get('output', '无结果')}")


def test_technical_prompt():
    """
    测试技术性提示词优化
    
    场景：包含专业术语的技术任务
    """
    print("\n" + "="*60)
    print("测试5：技术性提示词优化")
    print("="*60)
    
    agent = build_prompt_optimization_agent(verbose=True)
    
    technical_prompt = "使用Playwright爬取SPA应用的动态内容"
    
    print(f"原始输入: {technical_prompt}")
    print("-" * 40)
    
    # Agent会搜索Playwright、SPA等技术术语
    result = agent.invoke(technical_prompt)
    
    print(f"优化结果:\n{result.get('output', '无结果')}")


def main():
    """
    主函数：运行所有测试
    
    注意：需要配置.env文件中的：
    - OPENAI_API_KEY
    - SERPAPI_API_KEY
    """
    print("""
╔══════════════════════════════════════════════════════════╗
║         PromptOptimizationAgent 测试示例                  ║
║                                                           ║
║  演示新的4个核心LLM驱动工具：                             ║
║  1. generate_standard_template - 生成理想模板            ║
║  2. search_web_context - 智能搜索网络信息                ║
║  3. generate_clarifying_questions - 生成澄清问题         ║
║  4. generate_optimized_prompt - 输出优化提示词           ║
╚══════════════════════════════════════════════════════════╝
    """)
    
    # 验证配置
    if not config.OPENAI_API_KEY:
        print("❌ 错误：请在.env文件中配置OPENAI_API_KEY")
        return
    
    print("配置验证通过，开始测试...\n")
    
    # 运行测试
    try:
        # 测试1：简单提示词
        test_simple_prompt_optimization()
        
        # 测试2：复杂提示词
        # test_complex_prompt_optimization()
        
        # 测试3：带网站的提示词
        # test_with_specific_website()
        
        # 测试4：交互式流程
        # test_interactive_optimization()
        
        # 测试5：技术性提示词
        # test_technical_prompt()
        
        print("\n" + "="*60)
        print("✅ 测试完成！")
        print("="*60)
        
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()