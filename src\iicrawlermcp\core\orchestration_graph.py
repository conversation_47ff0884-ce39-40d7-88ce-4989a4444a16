"""
LangGraph编排图

使用LangGraph重新实现任务编排，自动管理Agent间的状态和上下文。
这个模块将逐步替换原有的orchestration_tools.py。
"""

import logging
from typing import Dict, Any, Literal
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from .graph_state import CrawlerState, create_initial_state
from .agent_adapter import get_or_create_agent_node

logger = logging.getLogger(__name__)


def _cleanup_browser():
    """
    统一的浏览器清理函数
    """
    try:
        from ..core.browser import close_global_browser
        close_global_browser()
        logger.info("<PERSON>rowser closed successfully")
    except Exception as e:
        logger.warning(f"Failed to close browser: {e}")


def should_continue(state: CrawlerState) -> Literal["continue", "end"]:
    """
    简化的继续判断逻辑
    
    Args:
        state: 当前状态
        
    Returns:
        "continue" 或 "end"
    """
    # 任务完成
    if state.get("is_complete", False):
        logger.info("Task completed, ending workflow")
        return "end"
    
    # 错误过多
    if state.get("error") and state.get("retry_count", 0) >= 3:
        logger.warning(f"Max retries reached: {state.get('retry_count')}")
        return "end"
    
    # 步骤过多（防止无限循环）
    execution_results = state.get("execution_results", [])
    if len(execution_results) > 10:
        logger.warning(f"Too many execution steps ({len(execution_results)}), stopping")
        return "end"
    
    return "continue"




def create_crawler_graph():
    """
    创建爬虫工作流图 - 简化版本

    流程: smart_planner → web → validator

    Returns:
        编译后的StateGraph
    """
    workflow = StateGraph(CrawlerState)

    # 添加所有节点 - 使用现有的agent
    nodes = ["smart_planner", "web", "validator"]
    for node in nodes:
        workflow.add_node(node, get_or_create_agent_node(node))

    logger.info("Created simplified workflow: smart_planner → web → validator")

    # 设置线性流程
    workflow.set_entry_point("smart_planner")
    workflow.add_edge("smart_planner", "web")
    workflow.add_edge("web", "validator")

    # 验证后的条件路由
    workflow.add_conditional_edges(
        "validator",
        should_continue,
        {"continue": "web", "end": END}
    )

    return workflow.compile(checkpointer=MemorySaver())


# 全局图实例（单例）
_global_graph = None


def get_or_create_graph():
    """
    获取或创建全局图实例（单例模式）
    """
    global _global_graph
    if _global_graph is None:
        _global_graph = create_crawler_graph()
        logger.info("Created new crawler graph")
    return _global_graph


def reset_global_graph():
    """
    重置全局图实例，强制重新创建
    """
    global _global_graph
    _global_graph = None
    logger.info("Graph reset - will recreate on next access")


def execute_with_streaming(task_description: str, config: Dict[str, Any] = None):
    """
    流式执行任务，实时返回进度更新
    
    Args:
        task_description: 任务描述
        config: 执行配置，默认使用thread_id="1"
        
    Yields:
        状态更新或错误信息
    """
    try:
        # 准备执行环境
        graph = get_or_create_graph()
        initial_state = create_initial_state(task_description)
        config = config or {"configurable": {"thread_id": "1"}}

        # 流式执行
        for state in graph.stream(initial_state, config):
            yield state
            
    except Exception as e:
        logger.error(f"Streaming execution failed: {e}")
        yield {"error": str(e)}
    finally:
        # 确保始终清理浏览器
        _cleanup_browser()
