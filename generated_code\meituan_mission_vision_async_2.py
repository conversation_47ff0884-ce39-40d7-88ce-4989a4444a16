import asyncio
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

MAX_RETRIES = 3

async def retry_async(func, *args, **kwargs):
    for attempt in range(1, MAX_RETRIES + 1):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logger.warning(f"Attempt {attempt} failed for {func.__name__}: {e}")
            if attempt == MAX_RETRIES:
                logger.error(f"All {MAX_RETRIES} attempts failed for {func.__name__}")
                raise
            await asyncio.sleep(1)

async def navigate_browser(page, url):
    async def _navigate():
        logger.info(f"Navigating to {url}")
        await page.goto(url)
        await page.wait_for_load_state('load')
    await retry_async(_navigate)

async def click_element(page, element_selector):
    async def _click():
        logger.info(f"Clicking element: {element_selector}")
        await page.wait_for_selector(f"xpath={element_selector}", timeout=10000)
        await page.click(f"xpath={element_selector}")
        await page.wait_for_load_state('load')
    await retry_async(_click)

async def scroll_page(page, direction="down", pages=1, smooth=True):
    async def _scroll():
        logger.info(f"Scrolling page {direction} for {pages} page(s), smooth={smooth}")
        scroll_amount = 1000  # pixels per page
        for i in range(pages):
            if direction == "down":
                await page.evaluate(
                    """(scroll_amount, smooth) => {
                        window.scrollBy({ top: scroll_amount, behavior: smooth ? 'smooth' : 'auto' });
                    }""",
                    scroll_amount, smooth
                )
            elif direction == "up":
                await page.evaluate(
                    """(scroll_amount, smooth) => {
                        window.scrollBy({ top: -scroll_amount, behavior: smooth ? 'smooth' : 'auto' });
                    }""",
                    scroll_amount, smooth
                )
            await asyncio.sleep(1)  # Give time for smooth scrolling
    await retry_async(_scroll)

async def take_screenshot(page, filename, full_page=False, element_selector=None):
    async def _screenshot():
        logger.info(f"Taking screenshot: {filename}, full_page={full_page}, selector={element_selector}")
        Path(filename).parent.mkdir(parents=True, exist_ok=True)
        if element_selector:
            await page.wait_for_selector(f"xpath={element_selector}", timeout=10000)
            element = await page.query_selector(f"xpath={element_selector}")
            if not element:
                raise Exception(f"Element not found for screenshot: {element_selector}")
            await element.screenshot(path=filename)
        else:
            await page.screenshot(path=filename, full_page=full_page)
    await retry_async(_screenshot)

async def main():
    playwright = await async_playwright().start()
    browser = await playwright.chromium.launch(headless=True)
    context = await browser.new_context()
    page = await context.new_page()
    try:
        # 1. navigate_browser
        await navigate_browser(page, "https://www.meituan.com")

        # 2. click_element
        await click_element(page, "html/body/div[1]/div[2]/div[4]/div/div[3]/div[1]")

        # 3. scroll_page
        await scroll_page(page, direction="down", pages=1, smooth=True)

        # 4. click_element
        await click_element(page, "html/body/div[1]/div[1]/div[4]/div[2]/a")

        # 5. take_screenshot
        await take_screenshot(
            page,
            filename="meituan_mission_vision_section.png",
            full_page=False,
            element_selector="html/body/div[1]/div[2]/div[2]/section/div[1]"
        )

        # 6. take_screenshot
        await take_screenshot(
            page,
            filename="meituan_mission_vision_section_en.png",
            full_page=False,
            element_selector="html/body/div[1]/div[2]/div[2]/section/div[1]"
        )

    except Exception as e:
        logger.error(f"Automation failed: {e}")
    finally:
        await context.close()
        await browser.close()
        await playwright.stop()
        logger.info("Resources cleaned up.")

if __name__ == "__main__":
    asyncio.run(main())