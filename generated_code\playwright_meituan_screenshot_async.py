import asyncio
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

MAX_RETRIES = 3

async def retry_async(func, *args, **kwargs):
    last_exception = None
    for attempt in range(1, MAX_RETRIES + 1):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            last_exception = e
            logger.warning(f"Attempt {attempt} failed for {func.__name__}: {e}")
            if attempt < MAX_RETRIES:
                await asyncio.sleep(1)
    logger.error(f"All {MAX_RETRIES} attempts failed for {func.__name__}")
    raise last_exception

async def main():
    playwright = await async_playwright().start()
    browser = None
    context = None
    page = None
    try:
        browser = await playwright.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()

        # Step 1: navigate_browser
        url = "https://about.meituan.com/"
        async def navigate():
            await page.goto(url)
            await page.wait_for_load_state('domcontentloaded')
        await retry_async(navigate)
        logger.info(f"Navigated to {url}")

        # Step 2: take_screenshot
        element_selector = "html/body/div[1]/div[2]/div[2]/section/div[1]"
        filename = "meituan_mission_vision.png"

        async def take_screenshot():
            # Ensure the element is present
            element = await page.wait_for_selector(f"xpath={element_selector}", timeout=10000)
            # Create output directory if needed
            output_path = Path(filename)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            await element.screenshot(path=filename)
        await retry_async(take_screenshot)
        logger.info(f"Screenshot saved to {filename}")

    except Exception as e:
        logger.error(f"Automation failed: {e}")
    finally:
        if page:
            await page.close()
        if context:
            await context.close()
        if browser:
            await browser.close()
        await playwright.stop()

if __name__ == "__main__":
    asyncio.run(main())