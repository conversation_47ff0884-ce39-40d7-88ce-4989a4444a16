"""
Validator Agent module for task completion validation.

This module provides a specialized agent that validates task execution results
and determines if tasks are completed successfully. Following the project
pattern, tools are defined separately and imported.
"""

import logging
from typing import Optional, List
from .prompts import get_validator_agent_prompt
from langchain.agents import create_openai_functions_agent, AgentExecutor
from langchain_openai import ChatOpenAI
from langchain_core.tools import BaseTool

from ..core.config import config

logger = logging.getLogger(__name__)


class ValidatorAgent:
    """
    Task validation specialist agent.

    This agent is responsible for validating task completion and assessing
    result quality. It uses LLM to understand task requirements and judge
    whether the execution results meet the expected outcomes.
    """

    def __init__(
        self,
        tools: Optional[List[BaseTool]] = None,
        verbose: Optional[bool] = None,
        llm_config: Optional[dict] = None,
    ):
        """
        Initialize the ValidatorAgent.

        Args:
            tools: List of LangChain tools to use. If None, uses default validation tools.
            verbose: Whether to enable verbose logging.
            llm_config: Custom LLM configuration.
        """
        if tools is None:
            from ..tools.validation_tools import get_validation_tools

            self.tools = get_validation_tools()
            logger.info(
                f"ValidatorAgent initialized with {len(self.tools)} validation tools"
            )
        else:
            self.tools = tools

        self.verbose = verbose if verbose is not None else config.VERBOSE
        self.llm_config = llm_config or config.get_llm_config()

        self._llm = None
        self._agent = None
        self._executor = None

    def _create_llm(self) -> ChatOpenAI:
        """Create and configure the LLM instance."""
        if self._llm is None:
            try:
                self._llm = ChatOpenAI(**self.llm_config)
                logger.info(f"LLM created with model: {self.llm_config.get('model')}")
            except Exception as e:
                logger.error(f"Failed to create LLM: {e}")
                raise
        return self._llm

    def _create_agent(self) -> None:
        """Create the LangChain agent."""
        if self._agent is None:
            try:
                llm = self._create_llm()
                prompt = get_validator_agent_prompt()
                self._agent = create_openai_functions_agent(llm, self.tools, prompt)
                logger.info("ValidatorAgent created successfully")
            except Exception as e:
                logger.error(f"Failed to create agent: {e}")
                raise

    def _create_executor(self) -> AgentExecutor:
        """Create the agent executor."""
        if self._executor is None:
            self._create_agent()
            try:
                self._executor = AgentExecutor(
                    agent=self._agent,
                    tools=self.tools,
                    verbose=self.verbose,
                    max_iterations=10,
                    handle_parsing_errors=True,
                )
                logger.info("ValidatorAgent executor created successfully")
            except Exception as e:
                logger.error(f"Failed to create agent executor: {e}")
                raise
        return self._executor

    def invoke(self, input_text: str) -> dict:
        """
        Validate task completion based on the task and results.

        Args:
            input_text: Task description and execution results to validate

        Returns:
            A dictionary containing validation results
        """
        executor = self._create_executor()

        try:
            logger.info(f"ValidatorAgent validating: {input_text[:100]}...")
            result = executor.invoke({"input": input_text})
            logger.info("ValidatorAgent validation completed")
            return result
        except Exception as e:
            logger.error(f"ValidatorAgent validation failed: {e}")
            raise

    def cleanup(self) -> None:
        """Clean up resources used by the agent."""
        try:
            self._llm = None
            self._agent = None
            self._executor = None
            logger.info("ValidatorAgent cleanup completed")
        except Exception as e:
            logger.error(f"Error during ValidatorAgent cleanup: {e}")


def build_validator_agent(
    tools: Optional[List[BaseTool]] = None,
    verbose: Optional[bool] = None,
    llm_config: Optional[dict] = None,
) -> ValidatorAgent:
    """
    Build and return a configured ValidatorAgent instance.

    Args:
        tools: List of LangChain tools to use
        verbose: Whether to enable verbose logging
        llm_config: Custom LLM configuration

    Returns:
        A configured ValidatorAgent instance
    """
    try:
        agent = ValidatorAgent(tools=tools, verbose=verbose, llm_config=llm_config)
        logger.info("ValidatorAgent built successfully")
        return agent
    except Exception as e:
        logger.error(f"Failed to build ValidatorAgent: {e}")
        raise
