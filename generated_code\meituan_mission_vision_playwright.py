import asyncio
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# XPath selectors from dom_get_content_elements output
XPATHS = [
    "html/body/div[1]/div[2]/div[2]/section/div[1]/div[1]/span[2]",
    "html/body/div[1]/div[2]/div[2]/section/div[1]/div[1]"
]

async def retry_async(fn, retries=3, *args, **kwargs):
    last_exc = None
    for attempt in range(1, retries + 1):
        try:
            return await fn(*args, **kwargs)
        except Exception as e:
            last_exc = e
            logger.warning(f"Attempt {attempt} failed with error: {e}")
            if attempt < retries:
                await asyncio.sleep(1)
    logger.error(f"All {retries} attempts failed.")
    raise last_exc

async def navigate(page, url):
    async def _navigate():
        logger.info(f"Navigating to {url}")
        await page.goto(url)
        await page.wait_for_load_state('load')
    await retry_async(_navigate)

async def scroll_page(page, direction="down", pages=1, smooth=True):
    async def _scroll():
        logger.info(f"Scrolling page {direction} for {pages} pages, smooth={smooth}")
        for _ in range(pages):
            await page.evaluate(
                """(direction, smooth) => {
                    const height = window.innerHeight;
                    window.scrollBy({ top: direction === 'down' ? height : -height, behavior: smooth ? 'smooth' : 'auto' });
                }""",
                direction, smooth
            )
            await asyncio.sleep(1)  # Wait for scroll to finish
    await retry_async(_scroll)

async def get_content_elements(page, xpaths):
    results = []
    for xpath in xpaths:
        selector = f"xpath={xpath}"
        async def _get_text():
            logger.info(f"Getting content from {selector}")
            element = await page.wait_for_selector(selector, timeout=5000)
            text = await element.inner_text()
            logger.info(f"Found text: {text.strip()[:50]}...")
            return text
        try:
            text = await retry_async(_get_text)
            results.append({'xpath': xpath, 'text': text})
        except PlaywrightTimeoutError:
            logger.error(f"Timeout: Element not found for {selector}")
        except Exception as e:
            logger.error(f"Error getting content from {selector}: {e}")
    return results

async def main():
    playwright = await async_playwright().start()
    browser = await playwright.chromium.launch(headless=True)
    context = await browser.new_context()
    page = await context.new_page()
    try:
        # 1. Navigate to Meituan
        await navigate(page, "https://www.meituan.com")

        # 2. Scroll down one page smoothly
        await scroll_page(page, direction="down", pages=1, smooth=True)

        # 3. Get content elements by XPath
        elements = await get_content_elements(page, XPATHS)
        for elem in elements:
            logger.info(f"XPath: {elem['xpath']}\nText: {elem['text']}\n{'-'*40}")

    except Exception as e:
        logger.error(f"Script failed: {e}")
    finally:
        await context.close()
        await browser.close()
        await playwright.stop()
        logger.info("Resources cleaned up.")

if __name__ == "__main__":
    asyncio.run(main())