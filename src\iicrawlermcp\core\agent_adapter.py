"""
Agent节点适配器

将现有的Agent类适配为LangGraph节点，实现平滑迁移。
这样可以复用所有现有Agent代码，无需重写。
"""

import logging
import json
import re
from typing import Dict, Any, Optional, Callable
from langchain_core.messages import AIMessage, HumanMessage
from .graph_state import CrawlerState

logger = logging.getLogger(__name__)

def _parse_message_content(content):
    """
    解析消息内容，支持字符串和content block格式
    
    LangGraph Studio使用Anthropic风格的content blocks：
    [{'type': 'text', 'text': '...'}]
    
    Args:
        content: 消息内容，可能是字符串或content block列表
        
    Returns:
        str: 解析后的文本内容
    """
    if isinstance(content, str):
        return content
    
    if isinstance(content, list) and len(content) > 0:
        # 处理 [{'type': 'text', 'text': '...'}] 格式
        if isinstance(content[0], dict) and content[0].get('type') == 'text':
            return content[0].get('text', '')
        # 处理其他列表格式
        return ' '.join(str(item) for item in content)
    
    # 默认转换为字符串
    return str(content)

# 常量定义
class MessageKeywords:
    """消息内容关键词常量"""
    NAVIGATE_TO_CN = "导航到"
    NAVIGATE_TO_EN = "navigated to"
    VALIDATION_FAILED_CN = "验证失败"
    VALIDATION_FAILED_EN = "validation failed"
    IS_COMPLETE = "is_complete"
    FALSE = "false"
    SEARCH_CN = "搜索"
    SEARCH_EN = "search"


class AgentNodeAdapter:
    """
    将现有Agent包装为LangGraph节点

    这个适配器允许我们复用所有现有的Agent类，
    只需要包装成节点函数即可在LangGraph中使用。
    """

    def __init__(
        self,
        agent_instance: Any,
        agent_name: str,
        extract_input: Optional[Callable] = None,
        process_output: Optional[Callable] = None,
    ):
        """
        初始化适配器

        Args:
            agent_instance: Agent实例（如BrowserAgent、ElementAgent等）
            agent_name: Agent名称，用于日志和状态管理
            extract_input: 自定义输入提取函数
            process_output: 自定义输出处理函数
        """
        self.agent = agent_instance
        self.agent_name = agent_name
        self.extract_input = extract_input
        self.process_output = process_output

    def __call__(self, state: CrawlerState) -> Dict[str, Any]:
        """
        节点执行函数

        将LangGraph状态转换为Agent输入，执行Agent，
        然后将结果更新回状态。

        Args:
            state: 当前爬虫状态

        Returns:
            状态更新字典
        """
        try:
            logger.info(f"Executing {self.agent_name} node")

            # 1. 提取输入
            if self.extract_input:
                input_text = self.extract_input(state)
            else:
                # 默认：使用最后一条消息作为输入
                input_text = self._default_extract_input(state)

            logger.debug(f"{self.agent_name} input: {input_text[:100]}...")

            # 2. 调用原有Agent
            result = self.agent.invoke(input_text)

            # 3. 处理输出
            if self.process_output:
                updates = self.process_output(state, result)
            else:
                updates = self._default_process_output(state, result)

            logger.info(f"{self.agent_name} execution completed")
            return updates

        except Exception as e:
            logger.error(f"{self.agent_name} execution failed: {e}")
            return {
                "error": str(e),
                "messages": [
                    AIMessage(content=f"❌ {self.agent_name}执行失败: {str(e)}")
                ],
            }

    def _default_extract_input(self, state: CrawlerState) -> str:
        """默认输入提取：为每个Agent提供适当的任务上下文"""

        # 如果有特定的任务在context_variables中
        if "current_task" in state.get("context_variables", {}):
            return state["context_variables"]["current_task"]

        # 获取最新的用户任务（最后一条HumanMessage）
        latest_task = ""
        original_task = ""
        if state.get("messages"):
            # 查找最后一条HumanMessage作为当前任务
            for msg in reversed(state["messages"]):
                if hasattr(msg, 'type') and msg.type == 'human':
                    latest_task = _parse_message_content(msg.content)
                    break
            # 备选：如果没有找到HumanMessage，使用最后一条消息
            if not latest_task:
                latest_task = _parse_message_content(state["messages"][-1].content)
            # 保留第一条消息作为原始任务引用
            original_task = _parse_message_content(state["messages"][0].content)

        # 根据不同的Agent返回适当的任务上下文
        if self.agent_name == "web":
            # WebAgent应该接收Planner生成的具体执行计划
            # 查找最后一条来自planner的消息（包含执行计划）
            messages = state.get("messages", [])
            
            # 查找包含执行计划的消息
            execution_plan = ""
            for msg in reversed(messages):
                if hasattr(msg, 'content'):
                    content = _parse_message_content(msg.content)
                    # Planner的输出通常包含"执行计划"或具体的步骤
                    if ("执行计划" in content or 
                        "步骤" in content or 
                        "Step" in content or
                        "1." in content or
                        "navigate" in content.lower() or
                        "打开" in content):
                        execution_plan = content
                        break
            
            # 如果找到了执行计划，使用它；否则使用原始任务
            if execution_plan:
                logger.debug(f"WebAgent using execution plan: {execution_plan[:200]}...")
                return execution_plan
            
            # 备选：检查是否是WebAgent的首次执行
            execution_count = sum(1 for r in state.get("execution_results", []) if r.get("agent") == "web")
            if execution_count == 0:
                # 首次执行，使用原始任务并强调要实际执行
                return f"""立即执行以下任务，不要只是计划或分析：
                
任务：{latest_task}

具体要求：
1. 如果任务包含"打开"某个网站，使用navigate_browser导航到该网站
2. 如果任务包含"搜索"，在搜索框输入关键词并提交
3. 如果任务包含"点击"，找到元素并点击
4. 必须执行实际操作，不能只返回分析结果
5. 完成后截图作为证明

现在开始执行！"""
            
            # WebAgent需要知道当前页面状态和需要执行的具体操作
            last_message = _parse_message_content(messages[-1].content) if messages else ""

            # 从最新任务中提取搜索关键词或操作
            if MessageKeywords.SEARCH_CN in latest_task or MessageKeywords.SEARCH_EN in latest_task.lower():
                # 提取搜索关键词
                search_term = _extract_search_term(latest_task)

                if search_term and (
                    MessageKeywords.NAVIGATE_TO_CN in last_message or MessageKeywords.NAVIGATE_TO_EN in last_message.lower()
                ):
                    # 页面已经导航，现在需要执行搜索
                    return f"在当前页面的搜索框中输入'{search_term}'并执行搜索，然后获取第一个搜索结果"

            # 如果是其他任务，提供具体的操作指令
            if (
                MessageKeywords.VALIDATION_FAILED_CN in last_message
                or MessageKeywords.VALIDATION_FAILED_EN in last_message.lower()
            ):
                # 验证失败，需要完成最新任务
                return f"任务未完成，请执行：{latest_task}"

            # 如果是验证失败的结果，需要重新理解任务
            if MessageKeywords.IS_COMPLETE in last_message and MessageKeywords.FALSE in last_message.lower():
                # 从最新任务提取需要的操作
                return latest_task

        # 默认情况：优先使用最新的用户任务，备选最后一条消息
        return latest_task if latest_task else ""

    def _default_process_output(self, state: CrawlerState, result: Dict) -> Dict:
        """默认输出处理：更新消息和执行结果"""
        output = result.get("output", str(result))

        updates = {
            "messages": [AIMessage(content=output)],
            "execution_results": [{"agent": self.agent_name, "result": result}],
        }

        # 如果Agent返回了特定的状态更新
        if isinstance(result, dict) and "state_updates" in result:
            updates.update(result["state_updates"])

        return updates

def _extract_json_from_text(text: str) -> Optional[Dict]:
        """
        从文本中提取JSON对象的统一方法
        
        支持两种情况：
        1. 文本以{开头的完整JSON
        2. 文本中嵌入的JSON对象
        """
        import json
        
        # 尝试多个起始位置
        start_positions = [0] if text.strip().startswith("{") else [text.find("{")]
        
        for start_idx in start_positions:
            if start_idx == -1:
                continue
                
            try:
                # 使用大括号计数法找到完整JSON
                brace_count = 0
                for i, char in enumerate(text[start_idx:], start_idx):
                    if char == "{":
                        brace_count += 1
                    elif char == "}":
                        brace_count -= 1
                        if brace_count == 0:
                            json_str = text[start_idx:i+1]
                            return json.loads(json_str)
            except (json.JSONDecodeError, ValueError):
                continue
                
        return None

def _extract_search_term(text: str) -> str:
        """
        从任务描述中提取搜索关键词的统一方法
        
        支持格式：
        - 单引号: '关键词'
        - 双引号: "关键词"  
        - 中文: 搜索 关键词
        - 英文: search for 关键词
        """
        import re
        
        # 按优先级顺序尝试提取
        patterns = [
            r"'([^']+)'",                          # 单引号
            r'"([^"]+)"',                          # 双引号
            r"搜索\s*['\"]?([^'\"，。\s]+)",          # 中文搜索
            r"search\s+for\s+['\"]?([^'\"，。\s]+)", # 英文搜索
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
                
        return ""


# Agent节点缓存，避免重复创建
_agent_nodes_cache = {}


def get_or_create_agent_node(agent_name: str) -> AgentNodeAdapter:
    """
    获取或创建Agent节点（单例模式）

    这解决了原架构中重复创建Agent实例的问题。

    Args:
        agent_name: Agent名称

    Returns:
        Agent节点适配器
    """
    if agent_name not in _agent_nodes_cache:
        _agent_nodes_cache[agent_name] = _create_agent_node(agent_name)
    return _agent_nodes_cache[agent_name]


def _create_agent_node(agent_name: str) -> AgentNodeAdapter:
    """
    创建特定的Agent节点

    Args:
        agent_name: Agent名称

    Returns:
        Agent节点适配器
    """
    if agent_name == "validator":
        from ..agents.validator_agent import build_validator_agent

        agent = build_validator_agent()

        def extract_validator_input(state):
            messages = state.get("messages", [])
            # 获取最新的用户任务
            latest_task = ""
            for msg in reversed(messages):
                if hasattr(msg, 'type') and msg.type == 'human':
                    latest_task = _parse_message_content(msg.content)
                    break
            if not latest_task and messages:
                latest_task = _parse_message_content(messages[-1].content)
            results = json.dumps(state.get("execution_results", []), ensure_ascii=False)
            return f"验证任务是否完成：\n任务：{latest_task}\n执行结果：{results}"

        def process_validator_output(state, result):
            output = result.get("output", "")

            # 解析验证结果 - 提取JSON对象
            import re, json

            is_complete = False
            validation_data = _extract_json_from_text(output)
            
            if validation_data:
                is_complete = validation_data.get("is_complete", False)
                logger.info(f"Validator parsed is_complete: {is_complete}")
            else:
                logger.warning("Failed to extract JSON from validator output")
                logger.debug(f"Raw validator output: {output[:500]}...")

            updates = {
                "messages": [AIMessage(content=output)],
                "is_complete": is_complete,
                "execution_results": [{"agent": "validator", "result": result}],
            }

            logger.info(
                f"Validator returning state updates: is_complete={updates['is_complete']}"
            )
            return updates

        return AgentNodeAdapter(
            agent, "validator", extract_validator_input, process_validator_output
        )

    elif agent_name == "ask_human":
        """专门处理人机交互的节点"""
        from langgraph.types import interrupt
        
        def ask_human_node(state: CrawlerState) -> Dict[str, Any]:
            """检查是否需要询问用户并处理interrupt"""
            messages = state.get("messages", [])
            
            # 检查最后一条消息是否包含澄清问题
            if messages:
                last_message = messages[-1]
                if hasattr(last_message, 'content'):
                    content = _parse_message_content(last_message.content)
                    
                    # 检测是否包含澄清问题
                    if ("澄清问题" in content or
                        "请回答" in content or 
                        "为了更好地完成" in content or
                        "您需要" in content):
                        
                        logger.info("检测到澄清问题，触发interrupt等待用户输入")
                        
                        # 使用interrupt等待用户输入
                        user_response = interrupt(content)
                        
                        logger.info(f"收到用户回答: {str(user_response)[:100] if user_response else 'None'}...")
                        
                        # 更新context_variables
                        context_vars = state.get("context_variables", {}).copy()
                        context_vars["user_answers"] = user_response
                        context_vars["clarification_received"] = True
                        
                        return {
                            "messages": [HumanMessage(content=f"用户回答：{user_response}")],
                            "context_variables": context_vars,
                        }
            
            # 如果不需要用户输入，直接返回空
            return {}
        
        # 直接返回函数，不需要AgentNodeAdapter包装
        return ask_human_node
    
    elif agent_name == "smart_planner":
        from ..agents.smart_planner_agent import build_smart_planner_agent

        agent = build_smart_planner_agent()

        def extract_smart_planner_input(state):
            """为SmartPlannerAgent提取输入"""
            messages = state.get("messages", [])
            context_vars = state.get("context_variables", {})
            
            # 获取最新的用户任务
            latest_task = ""
            for msg in reversed(messages):
                if hasattr(msg, 'type') and msg.type == 'human':
                    content = _parse_message_content(msg.content)
                    # 跳过用户回答消息
                    if not content.startswith("用户回答："):
                        latest_task = content
                        break
            if not latest_task and messages:
                latest_task = _parse_message_content(messages[-1].content)
            
            # 如果有用户的澄清回答，将其包含在计划生成中
            if context_vars.get("clarification_received") and context_vars.get("user_answers"):
                return f"基于以下信息生成智能执行计划：\n任务：{latest_task}\n补充信息：{context_vars['user_answers']}"
            
            return f"理解并优化以下任务，生成详细执行计划：{latest_task}"

        def process_smart_planner_output(state, result):
            """处理SmartPlannerAgent的输出"""
            output = result.get("output", "")

            # 尝试解析计划
            plan_data = None
            try:
                import re
                json_match = re.search(r"\{[\s\S]*\}", output)
                if json_match:
                    plan_data = json.loads(json_match.group())
            except:
                pass

            # 检测是否包含澄清问题
            has_clarifying_questions = (
                "澄清问题" in output or
                "clarifying_questions" in output or
                "需要确认" in output or
                "请回答" in output
            )

            updates = {
                "messages": [AIMessage(content=output)],
                "task_plan": plan_data,
                "execution_results": [{"agent": "smart_planner", "result": result}],
            }

            # 如果有澄清问题，设置标记
            if has_clarifying_questions:
                context_vars = state.get("context_variables", {}).copy()
                context_vars["needs_clarification"] = True
                updates["context_variables"] = context_vars

            return updates

        return AgentNodeAdapter(
            agent, "smart_planner", extract_smart_planner_input, process_smart_planner_output
        )

    elif agent_name == "web":
        from ..agents.web_agent import build_web_agent

        agent = build_web_agent()

        def process_web_output(state, result):
            # 处理WebAgent的输出，更新URL和DOM状态
            output = result.get("output", "")
            
            # 自我验证逻辑：检查任务是否完成
            is_task_complete = False
            
            # 检查输出中的完成标志
            completion_keywords = [
                "任务完成", "task completed", "已完成", "successfully",
                "成功执行", "目标达成", "操作成功", "已找到",
                "数据已提取", "截图已保存"
            ]
            
            for keyword in completion_keywords:
                if keyword in output.lower():
                    is_task_complete = True
                    logger.info(f"WebAgent自我验证：检测到完成标志 '{keyword}'")
                    break
            
            # 检查是否有错误（错误过多时标记为完成，避免无限循环）
            retry_count = state.get("retry_count", 0)
            if "错误" in output or "error" in output.lower() or "失败" in output:
                retry_count += 1
                if retry_count >= 3:
                    logger.warning("WebAgent自我验证：错误次数过多，停止重试")
                    is_task_complete = True  # 防止无限循环
            
            # 提取工具调用历史（intermediate_steps）
            tool_calls = []
            if "intermediate_steps" in result:
                logger.info(f"WebAgent返回了 {len(result['intermediate_steps'])} 个中间步骤")
                for step in result["intermediate_steps"]:
                    if len(step) >= 2:  # intermediate_steps 格式: [(agent_action, tool_output), ...]
                        agent_action = step[0]
                        tool_output = step[1]
                        # 提取工具调用信息
                        if hasattr(agent_action, "tool"):
                            tool_call = {
                                "tool": agent_action.tool,
                                "tool_input": agent_action.tool_input,
                                "output": str(tool_output)[:500]  # 限制输出长度
                            }
                            tool_calls.append(tool_call)
                            logger.debug(f"提取工具调用: {agent_action.tool}")
            
            # 创建增强的结果对象，包含工具调用信息
            enhanced_result = result.copy()
            enhanced_result["tool_calls"] = tool_calls
            
            updates = {
                "messages": [AIMessage(content=output)],
                "execution_results": [{"agent": "web", "result": enhanced_result}],
                "is_complete": is_task_complete,
                "retry_count": retry_count,
            }

            # 更新当前URL（如果导航了）
            if MessageKeywords.NAVIGATE_TO_EN in output.lower() or MessageKeywords.NAVIGATE_TO_CN in output:
                import re

                url_match = re.search(r"https?://[^\s]+", output)
                if url_match:
                    updates["current_url"] = url_match.group()

            # 缓存DOM状态（如果有DOM分析结果）
            dom_info = result.get("dom_info", {})
            if dom_info:
                updates["dom_state"] = dom_info
            
            # 日志记录验证结果
            logger.info(f"WebAgent自我验证结果：is_complete={is_task_complete}, retry_count={retry_count}")

            return updates

        return AgentNodeAdapter(agent, "web", process_output=process_web_output)

    elif agent_name == "codegen":
        from ..agents.codegen_agent import build_codegen_agent

        agent = build_codegen_agent()

        def extract_codegen_input(state):
            """为CodeGenAgent提取输入：执行历史和用户任务"""
            messages = state.get("messages", [])
            execution_results = state.get("execution_results", [])
            
            # 获取原始用户任务
            original_task = ""
            if messages:
                original_task = _parse_message_content(messages[0].content)
            
            # 提取WebAgent的工具调用历史
            tool_calls = []
            for result in execution_results:
                if result.get("agent") == "web" and "result" in result:
                    agent_result = result["result"]
                    # 检查是否有工具调用信息
                    if isinstance(agent_result, dict) and "tool_calls" in agent_result:
                        tool_calls.extend(agent_result["tool_calls"])
                        logger.info(f"从WebAgent提取了 {len(agent_result['tool_calls'])} 个工具调用")
            
            # 如果有工具调用，构建详细的操作历史
            if tool_calls:
                # 转换为CodeGen期望的格式
                operations_data = {
                    "tool_calls": [
                        {
                            "name": call.get("tool", ""),
                            "args": call.get("tool_input", {}),
                            "output": call.get("output", "")
                        }
                        for call in tool_calls
                    ]
                }
                
                logger.info(f"准备传递给CodeGen的操作数据：{len(operations_data['tool_calls'])} 个操作")
                
                # 构建包含详细操作历史的输入
                input_text = f"""任务执行完成！

原始任务：{original_task}

详细操作历史：
{json.dumps(operations_data, ensure_ascii=False, indent=2)}

基于以上操作历史，是否需要生成可复用的Playwright Python代码？"""
            else:
                # 如果没有工具调用历史，使用简化的输出
                clean_results = []
                for result in execution_results:
                    clean_result = {
                        "agent": result.get("agent", ""),
                        "output": ""
                    }
                    
                    # 提取输出文本
                    if "result" in result:
                        agent_result = result["result"]
                        if isinstance(agent_result, dict):
                            clean_result["output"] = agent_result.get("output", str(agent_result))[:500]
                        else:
                            clean_result["output"] = str(agent_result)[:500]
                    
                    clean_results.append(clean_result)
                
                logger.warning("未找到详细的工具调用历史，使用简化输出")
                
                input_text = f"""任务执行完成！

原始任务：{original_task}

执行摘要：{json.dumps(clean_results, ensure_ascii=False, indent=2)}

注意：未能获取详细的操作历史。是否仍需要生成代码？"""
            
            return input_text

        def process_codegen_output(state, result):
            """处理CodeGenAgent的输出"""
            output = result.get("output", "")
            
            # 创建干净的结果记录（只包含可序列化的数据）
            clean_result = {
                "agent": "codegen",
                "result": {
                    "output": output
                }
            }
            
            updates = {
                "messages": [AIMessage(content=output)],
                "execution_results": [clean_result],
            }
            
            # 如果生成了代码文件，记录文件路径
            if "生成的文件位置" in output or "saved to" in output.lower():
                context_vars = state.get("context_variables", {}).copy()
                context_vars["code_generated"] = True
                updates["context_variables"] = context_vars
            
            return updates

        return AgentNodeAdapter(agent, "codegen", extract_codegen_input, process_codegen_output)

    else:
        raise ValueError(f"Unknown agent: {agent_name}")


def cleanup_agent_nodes():
    """清理所有Agent节点缓存"""
    global _agent_nodes_cache
    for node in _agent_nodes_cache.values():
        if hasattr(node.agent, "cleanup"):
            try:
                node.agent.cleanup()
            except:
                pass
    _agent_nodes_cache.clear()
