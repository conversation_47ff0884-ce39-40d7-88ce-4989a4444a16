import asyncio
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

MAX_RETRIES = 3

async def retry_async(func, *args, **kwargs):
    for attempt in range(1, MAX_RETRIES + 1):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logger.warning(f"Attempt {attempt} failed for {func.__name__}: {e}")
            if attempt == MAX_RETRIES:
                logger.error(f"All {MAX_RETRIES} attempts failed for {func.__name__}")
                raise
            await asyncio.sleep(1)

async def navigate_browser(page, url):
    async def _navigate():
        logger.info(f"Navigating to {url}")
        await page.goto(url)
        await page.wait_for_load_state('load')
    await retry_async(_navigate)

async def scroll_page(page, direction="down", pages=1, smooth=True):
    async def _scroll():
        logger.info(f"Scrolling page {direction} for {pages} page(s), smooth={smooth}")
        scroll_script = """
            async (direction, pages, smooth) => {
                const delay = ms => new Promise(res => setTimeout(res, ms));
                let scrollAmount = window.innerHeight;
                for (let i = 0; i < pages; i++) {
                    window.scrollBy({ 
                        top: direction === 'down' ? scrollAmount : -scrollAmount, 
                        left: 0, 
                        behavior: smooth ? 'smooth' : 'auto' 
                    });
                    await delay(smooth ? 700 : 200);
                }
            }
        """
        await page.evaluate(scroll_script, direction, pages, smooth)
        await asyncio.sleep(1)  # Give time for any lazy-loaded content
    await retry_async(_scroll)

async def take_screenshot(page, filename, element_selector):
    async def _screenshot():
        logger.info(f"Taking screenshot of element {element_selector} and saving as {filename}")
        element = await page.wait_for_selector(f"xpath={element_selector}", timeout=10000)
        Path(filename).parent.mkdir(parents=True, exist_ok=True)
        await element.screenshot(path=filename)
    await retry_async(_screenshot)

async def main():
    playwright = await async_playwright().start()
    browser = await playwright.chromium.launch(headless=True)
    context = await browser.new_context()
    page = await context.new_page()
    try:
        # Step 1: Navigate to Meituan
        await navigate_browser(page, "https://www.meituan.com/")

        # Step 2: Scroll down 1 page smoothly
        await scroll_page(page, direction="down", pages=1, smooth=True)

        # Step 3: Take screenshot of specified element
        await take_screenshot(
            page,
            filename="meituan_mission_vision.png",
            element_selector="html/body/div[1]/div[2]/div[2]/section/div[1]"
        )
        logger.info("Automation completed successfully.")
    except Exception as e:
        logger.error(f"Automation failed: {e}")
    finally:
        await context.close()
        await browser.close()
        await playwright.stop()

if __name__ == "__main__":
    asyncio.run(main())