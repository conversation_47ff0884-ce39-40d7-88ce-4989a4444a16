"""
PromptOptimizationAgent module for iICrawlerMCP.

This module provides a specialized agent for prompt analysis and optimization.
The PromptOptimizationAgent focuses on improving user prompts for better task execution.
"""

import logging
from typing import Optional, List
from .prompts import get_prompt_agent_prompt
from langchain.agents import create_openai_functions_agent, AgentExecutor
from langchain_openai import ChatOpenAI
from langchain_core.tools import BaseTool

from ..core.config import config

logger = logging.getLogger(__name__)


class PromptOptimizationAgent:
    """
    A specialized agent for prompt analysis and optimization.

    This agent analyzes user prompts and provides optimized versions
    with better clarity, structure, and effectiveness.
    """

    def __init__(
        self,
        tools: Optional[List[BaseTool]] = None,
        verbose: Optional[bool] = None,
        llm_config: Optional[dict] = None,
    ):
        """
        Initialize the PromptOptimizationAgent.

        Args:
            tools: List of LangChain tools to use. If None, uses optimization-specific tools.
            verbose: Whether to enable verbose logging. If None, uses config default.
            llm_config: Custom LLM configuration. If None, uses config defaults.
        """
        self.tools = tools or self._get_optimization_tools()
        self.verbose = verbose if verbose is not None else config.VERBOSE
        self.llm_config = llm_config or config.get_llm_config()

        self._llm = None
        self._agent = None
        self._executor = None

    def _get_optimization_tools(self) -> List[BaseTool]:
        """
        Get prompt optimization specific tools.

        PromptOptimizationAgent专注提示词分析和优化，
        使用4个核心LLM驱动工具和搜索工具。
        """
        from ..tools.prompt_tools import get_prompt_optimization_tools
        from ..tools.search_tools import web_search_serp

        # 获取提示词优化专用工具（4个核心工具）
        optimization_tools = get_prompt_optimization_tools()
        
        # 添加搜索工具以增强优化能力
        # 注意：web_search_serp已经被search_web_context内部调用
        # 这里不需要重复添加，避免工具重复
        
        logger.info(f"PromptOptimizationAgent configured with {len(optimization_tools)} tools")

        return optimization_tools

    def _create_llm(self) -> ChatOpenAI:
        """Create and configure the LLM instance."""
        if self._llm is None:
            try:
                self._llm = ChatOpenAI(**self.llm_config)
                logger.info(
                    f"PromptOptimizationAgent LLM created with model: {self.llm_config.get('model')}"
                )
            except Exception as e:
                logger.error(f"Failed to create PromptOptimizationAgent LLM: {e}")
                raise
        return self._llm

    def _create_agent(self) -> None:
        """Create the LangChain agent with prompt optimization specific prompt."""
        if self._agent is None:
            try:
                llm = self._create_llm()
                # Use prompt optimization specific prompt or the default one
                prompt = get_prompt_agent_prompt()
                self._agent = create_openai_functions_agent(llm, self.tools, prompt)
                logger.info("PromptOptimizationAgent created successfully")
            except Exception as e:
                logger.error(f"Failed to create PromptOptimizationAgent: {e}")
                raise

    def _create_executor(self) -> AgentExecutor:
        """Create the agent executor."""
        if self._executor is None:
            self._create_agent()
            try:
                self._executor = AgentExecutor(
                    agent=self._agent,
                    tools=self.tools,
                    verbose=self.verbose,
                    max_iterations=20,  # 提示词优化通常不需要太多迭代
                    handle_parsing_errors=True,
                )
                logger.info("PromptOptimizationAgent executor created successfully")
            except Exception as e:
                logger.error(f"Failed to create PromptOptimizationAgent executor: {e}")
                raise
        return self._executor

    def invoke(self, input_text: str) -> dict:
        """
        Execute a prompt optimization task using the agent.

        Args:
            input_text: The prompt optimization task description.

        Returns:
            A dictionary containing the agent's response and output.
        """
        executor = self._create_executor()

        try:
            logger.info(
                f"PromptOptimizationAgent executing task: {input_text[:100]}..."
            )
            result = executor.invoke({"input": input_text})
            logger.info("PromptOptimizationAgent task completed successfully")
            return result
        except Exception as e:
            logger.error(f"PromptOptimizationAgent task execution failed: {e}")
            raise


def build_prompt_optimization_agent(
    tools: Optional[List[BaseTool]] = None,
    verbose: Optional[bool] = None,
    llm_config: Optional[dict] = None,
) -> PromptOptimizationAgent:
    """
    Build and return a configured PromptOptimizationAgent instance.

    Args:
        tools: List of LangChain tools to use. If None, uses optimization-specific tools.
        verbose: Whether to enable verbose logging. If None, uses config default.
        llm_config: Custom LLM configuration. If None, uses config defaults.

    Returns:
        A configured PromptOptimizationAgent instance.

    Example:
        agent = build_prompt_optimization_agent()
        result = agent.invoke("优化这个提示词：帮我爬取网站数据")
    """
    # Validate configuration before creating agent
    config.validate()

    try:
        agent = PromptOptimizationAgent(
            tools=tools, verbose=verbose, llm_config=llm_config
        )
        logger.info("PromptOptimizationAgent built successfully")
        return agent
    except Exception as e:
        logger.error(f"Failed to build PromptOptimizationAgent: {e}")
        raise
