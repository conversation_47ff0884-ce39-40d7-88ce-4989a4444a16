"""
Planner Agent module for task planning and decomposition.

This module provides a specialized agent that analyzes tasks and generates
structured execution plans for the multi-agent system. Following the project
pattern, tools are defined separately and imported.
"""

import logging
from typing import Optional, List
from .prompts import get_planner_agent_prompt
from langchain.agents import create_openai_functions_agent, AgentExecutor
from langchain_openai import ChatOpenAI
from langchain_core.tools import BaseTool

from ..core.config import config

logger = logging.getLogger(__name__)


class PlannerAgent:
    """
    Task planning specialist agent.

    This agent is responsible for analyzing tasks and generating execution plans.
    It uses LLM to understand task requirements and create structured plans
    without complex rules - letting the LLM make intelligent decisions.
    """

    def __init__(
        self,
        tools: Optional[List[BaseTool]] = None,
        verbose: Optional[bool] = None,
        llm_config: Optional[dict] = None,
    ):
        """
        Initialize the PlannerAgent.

        Args:
            tools: List of LangChain tools to use. If None, uses default planning tools.
            verbose: Whether to enable verbose logging.
            llm_config: Custom LLM configuration.
        """
        if tools is None:
            from ..tools.planning_tools import get_planning_tools
            from ..tools.search_tools import get_search_tools

            # 合并规划工具和搜索工具
            planning_tools = get_planning_tools()
            search_tools = get_search_tools()
            self.tools = planning_tools + search_tools
            logger.info(
                f"PlannerAgent initialized with {len(self.tools)} tools: {len(planning_tools)} planning + {len(search_tools)} search"
            )
        else:
            self.tools = tools

        self.verbose = verbose if verbose is not None else config.VERBOSE
        self.llm_config = llm_config or config.get_llm_config()

        self._llm = None
        self._agent = None
        self._executor = None

    def _create_llm(self) -> ChatOpenAI:
        """Create and configure the LLM instance."""
        if self._llm is None:
            try:
                self._llm = ChatOpenAI(**self.llm_config)
                logger.info(f"LLM created with model: {self.llm_config.get('model')}")
            except Exception as e:
                logger.error(f"Failed to create LLM: {e}")
                raise
        return self._llm

    def _create_agent(self) -> None:
        """Create the LangChain agent."""
        if self._agent is None:
            try:
                llm = self._create_llm()
                prompt = get_planner_agent_prompt()
                self._agent = create_openai_functions_agent(llm, self.tools, prompt)
                logger.info("PlannerAgent created successfully")
            except Exception as e:
                logger.error(f"Failed to create agent: {e}")
                raise

    def _create_executor(self) -> AgentExecutor:
        """Create the agent executor."""
        if self._executor is None:
            self._create_agent()
            try:
                self._executor = AgentExecutor(
                    agent=self._agent,
                    tools=self.tools,
                    verbose=self.verbose,
                    max_iterations=10,
                    handle_parsing_errors=True,
                )
                logger.info("PlannerAgent executor created successfully")
            except Exception as e:
                logger.error(f"Failed to create agent executor: {e}")
                raise
        return self._executor

    def invoke(self, input_text: str) -> dict:
        """
        Generate an execution plan for the given task.

        Args:
            input_text: Task description to plan

        Returns:
            A dictionary containing the execution plan
        """
        executor = self._create_executor()

        try:
            logger.info(f"PlannerAgent generating plan for: {input_text}")
            result = executor.invoke({"input": input_text})
            logger.info("PlannerAgent plan generation completed")
            return result
        except Exception as e:
            logger.error(f"PlannerAgent planning failed: {e}")
            raise

    def cleanup(self) -> None:
        """Clean up resources used by the agent."""
        try:
            self._llm = None
            self._agent = None
            self._executor = None
            logger.info("PlannerAgent cleanup completed")
        except Exception as e:
            logger.error(f"Error during PlannerAgent cleanup: {e}")


def build_planner_agent(
    tools: Optional[List[BaseTool]] = None,
    verbose: Optional[bool] = None,
    llm_config: Optional[dict] = None,
) -> PlannerAgent:
    """
    Build and return a configured PlannerAgent instance.

    Args:
        tools: List of LangChain tools to use
        verbose: Whether to enable verbose logging
        llm_config: Custom LLM configuration

    Returns:
        A configured PlannerAgent instance
    """
    try:
        agent = PlannerAgent(tools=tools, verbose=verbose, llm_config=llm_config)
        logger.info("PlannerAgent built successfully")
        return agent
    except Exception as e:
        logger.error(f"Failed to build PlannerAgent: {e}")
        raise
