"""
Validation tools for task completion verification.

This module provides tools that enable intelligent validation of task execution
results. It moves the concept of dom_assess_information_completeness to a 
higher level - validating entire task completion rather than just DOM content.
"""

import json
import logging
from langchain_core.tools import tool

logger = logging.getLogger(__name__)


@tool
def validate_task_completion(task_description: str, execution_results: str) -> str:
    """
    验证任务是否完成 - 让LLM判断执行结果是否满足任务要求。

    这是dom_assess_information_completeness的升级版，不仅检查信息完整性，
    还验证整个任务的完成情况。

    Args:
        task_description: 原始任务描述
        execution_results: 执行结果（可以是文本、JSON或描述）

    Returns:
        验证结果的JSON字符串，包含：
        {{
            "is_complete": true/false,
            "completeness_score": 0-100,
            "validation_details": "详细说明",
            "missing_items": ["缺失项1", "缺失项2"],
            "suggestions": ["建议1", "建议2"],
            "need_retry": true/false
        }}

    Example:
        validate_task_completion(
            "查找所有产品价格",
            "找到3个产品：产品A $10, 产品B $20, 产品C $30"
        )
    """
    try:
        from langchain_openai import ChatOpenAI
        from ..core.config import config

        llm = ChatOpenAI(**config.get_llm_config())

        prompt = f"""
你是一个任务验证专家。请判断以下任务是否成功完成。

原始任务：{task_description}

执行结果：
{execution_results}

请从以下维度评估：
1. 任务目标是否达成
2. 结果的完整性（0-100分）
3. 是否有遗漏的部分
4. 结果质量是否满意

返回JSON格式的验证结果：
{{
    "is_complete": true/false,
    "completeness_score": 85,
    "validation_details": "详细验证说明",
    "missing_items": ["缺失的内容"],
    "suggestions": ["改进建议"],
    "need_retry": true/false
}}

请直接返回JSON，不要其他内容。
"""

        response = llm.invoke(prompt)
        validation_json = response.content.strip()

        # 尝试解析JSON
        try:
            validation = json.loads(validation_json)
            logger.info(
                f"Validation complete: {validation.get('completeness_score')}% complete"
            )
            return validation_json
        except json.JSONDecodeError:
            # 提取JSON
            import re

            json_match = re.search(r"\{[\s\S]*\}", validation_json)
            if json_match:
                return json_match.group()

            # 返回默认验证结果
            return json.dumps(
                {
                    "is_complete": False,
                    "completeness_score": 50,
                    "validation_details": "无法解析验证结果",
                    "missing_items": [],
                    "suggestions": ["重新执行任务"],
                    "need_retry": True,
                },
                ensure_ascii=False,
            )

    except Exception as e:
        logger.error(f"Failed to validate task completion: {e}")
        return json.dumps(
            {
                "error": str(e),
                "is_complete": False,
                "completeness_score": 0,
                "need_retry": True,
            },
            ensure_ascii=False,
        )


@tool
def assess_information_completeness(
    user_query: str, collected_data: str, page_context: str = ""
) -> str:
    """
    评估信息完整性 - 基于dom_assess_information_completeness的概念，
    但应用于更广泛的任务验证场景。

    Args:
        user_query: 用户的原始查询
        collected_data: 已收集的数据
        page_context: 页面上下文信息（可选）

    Returns:
        完整性评估结果，包括是否需要继续收集数据

    Example:
        assess_information_completeness(
            "获取所有用户评论",
            "已收集10条评论",
            "页面显示'查看更多评论'按钮"
        )
    """
    try:
        from langchain_openai import ChatOpenAI
        from ..core.config import config

        llm = ChatOpenAI(**config.get_llm_config())

        prompt = f"""
评估数据收集的完整性：

用户需求：{user_query}

已收集数据：
{collected_data}

页面上下文：
{page_context if page_context else "无额外上下文"}

请分析：
1. 用户期望获得多少数据？
2. 当前数据是否满足用户需求？
3. 是否有迹象表明还有更多数据？（如"下一页"、"查看更多"等）
4. 数据质量是否符合要求？

输出格式：
- 完整性评分（0-100）
- 是否需要继续收集
- 具体建议
"""

        response = llm.invoke(prompt)
        assessment = response.content.strip()

        logger.info("Information completeness assessed")
        return assessment

    except Exception as e:
        logger.error(f"Failed to assess information completeness: {e}")
        return f"评估失败：{str(e)}"


@tool
def identify_missing_steps(
    task_description: str, completed_steps: str, current_results: str
) -> str:
    """
    识别缺失的执行步骤 - 让LLM分析还需要哪些步骤才能完成任务。

    Args:
        task_description: 原始任务描述
        completed_steps: 已完成的步骤列表
        current_results: 当前的执行结果

    Returns:
        缺失步骤的分析和建议

    Example:
        identify_missing_steps(
            "登录并下载报告",
            "已完成：1.导航到登录页",
            "当前在登录页面"
        )
    """
    try:
        from langchain_openai import ChatOpenAI
        from ..core.config import config

        llm = ChatOpenAI(**config.get_llm_config())

        prompt = f"""
分析任务完成情况，识别缺失的步骤：

目标任务：{task_description}

已完成步骤：
{completed_steps}

当前结果：
{current_results}

请识别：
1. 还需要哪些步骤才能完成任务？
2. 这些步骤的执行顺序
3. 每个步骤需要哪个Agent执行

返回具体的补充步骤建议。
"""

        response = llm.invoke(prompt)
        missing_analysis = response.content.strip()

        logger.info("Missing steps identified")
        return missing_analysis

    except Exception as e:
        logger.error(f"Failed to identify missing steps: {e}")
        return f"分析失败：{str(e)}"


@tool
def generate_retry_strategy(
    task_description: str, failure_reason: str, previous_attempts: str = ""
) -> str:
    """
    生成重试策略 - 当任务失败时，让LLM分析原因并提供新的执行策略。

    Args:
        task_description: 任务描述
        failure_reason: 失败原因
        previous_attempts: 之前的尝试记录

    Returns:
        新的执行策略建议
    """
    try:
        from langchain_openai import ChatOpenAI
        from ..core.config import config

        llm = ChatOpenAI(**config.get_llm_config())

        prompt = f"""
任务执行失败，需要新的策略：

任务：{task_description}

失败原因：{failure_reason}

之前的尝试：
{previous_attempts if previous_attempts else "首次尝试"}

请提供：
1. 失败的根本原因分析
2. 新的执行策略
3. 需要调整的具体步骤
4. 预防类似失败的建议

简洁明了地给出可行的重试方案。
"""

        response = llm.invoke(prompt)
        retry_strategy = response.content.strip()

        logger.info("Retry strategy generated")
        return retry_strategy

    except Exception as e:
        logger.error(f"Failed to generate retry strategy: {e}")
        return f"无法生成重试策略：{str(e)}"


# 工具列表
VALIDATION_TOOLS = [
    validate_task_completion,
    assess_information_completeness,
    identify_missing_steps,
    generate_retry_strategy,
]


def get_validation_tools():
    """
    Get validation tools for ValidatorAgent.

    Returns:
        List of validation tools
    """
    return VALIDATION_TOOLS.copy()
