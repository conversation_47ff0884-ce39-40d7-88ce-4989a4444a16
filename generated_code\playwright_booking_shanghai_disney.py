import asyncio
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

MAX_RETRIES = 3

async def retry_async(fn, *args, **kwargs):
    for attempt in range(1, MAX_RETRIES + 1):
        try:
            return await fn(*args, **kwargs)
        except PlaywrightTimeoutError as e:
            logger.warning(f"Timeout on attempt {attempt} for {fn.__name__}: {e}")
        except Exception as e:
            logger.warning(f"Error on attempt {attempt} for {fn.__name__}: {e}")
        if attempt < MAX_RETRIES:
            await asyncio.sleep(1)
    raise Exception(f"Failed after {MAX_RETRIES} attempts: {fn.__name__}")

async def main():
    playwright = await async_playwright().start()
    browser = None
    page = None
    try:
        browser = await playwright.chromium.launch(headless=False)
        page = await browser.new_page()

        # Step 1: navigate_browser
        url = "https://www.booking.com"
        logger.info(f"Navigating to {url}")
        await retry_async(page.goto, url)
        await retry_async(page.wait_for_load_state, 'load')

        # Step 2: type_text
        input_xpath = "html/body/div[3]/div[2]/div/div/form/div/div[1]/div/div/div[1]/div/div/input"
        input_selector = f"xpath={input_xpath}"
        text_to_type = "上海迪士尼"
        logger.info(f"Typing '{text_to_type}' into {input_selector}")
        await retry_async(page.wait_for_selector, input_selector, timeout=10000)
        await retry_async(page.fill, input_selector, text_to_type)

        # Step 3: click_element (suggestion)
        suggestion_xpath = "html/body/div[3]/div[2]/div/div/form/div/div[1]/div/div/div[2]/div/div/ul/li[1]/div"
        suggestion_selector = f"xpath={suggestion_xpath}"
        logger.info(f"Clicking suggestion {suggestion_selector}")
        await retry_async(page.wait_for_selector, suggestion_selector, timeout=10000)
        await retry_async(page.click, suggestion_selector)

        # Step 4: click_element (search button)
        search_btn_xpath = "html/body/div[3]/div[2]/div/div/form/div/div[2]/div/button"
        search_btn_selector = f"xpath={search_btn_xpath}"
        logger.info(f"Clicking search button {search_btn_selector}")
        await retry_async(page.wait_for_selector, search_btn_selector, timeout=10000)
        await retry_async(page.click, search_btn_selector)
        await retry_async(page.wait_for_load_state, 'load')

        logger.info("Automation steps completed successfully.")

    except Exception as e:
        logger.error(f"Automation failed: {e}")
    finally:
        if page:
            await page.close()
        if browser:
            await browser.close()
        await playwright.stop()

if __name__ == "__main__":
    asyncio.run(main())
