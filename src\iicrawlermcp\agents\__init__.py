"""
Agents module for iICrawlerMCP.

This module contains various AI agents for web automation tasks.
"""

from .web_agent import WebAgent, build_web_agent
from .prompt_agent import PromptOptimizationAgent, build_prompt_optimization_agent
from .planner_agent import PlannerAgent, build_planner_agent
from .validator_agent import ValidatorAgent, build_validator_agent
from .codegen_agent import CodeGenAgent, build_codegen_agent
from .smart_planner_agent import (
    SmartPlannerAgent, 
    build_smart_planner_agent,
    quick_analyze,
    quick_plan,
)

__all__ = [
    "WebAgent",
    "build_web_agent",
    "PromptOptimizationAgent",
    "build_prompt_optimization_agent",
    "PlannerAgent",
    "build_planner_agent",
    "ValidatorAgent",
    "build_validator_agent",
    "CodeGenAgent",
    "build_codegen_agent",
    # SmartPlannerAgent (unified planning and optimization)
    "SmartPlannerAgent",
    "build_smart_planner_agent",
    "quick_analyze",
    "quick_plan",
]
