import asyncio
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 重试装饰器
def retry_async(max_attempts=3, delay=1):
    def decorator(func):
        async def wrapper(*args, **kwargs):
            last_exc = None
            for attempt in range(1, max_attempts + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exc = e
                    logger.warning(f"Attempt {attempt} failed for {func.__name__}: {e}")
                    await asyncio.sleep(delay)
            logger.error(f"All {max_attempts} attempts failed for {func.__name__}")
            raise last_exc
        return wrapper
    return decorator

@retry_async()
async def navigate_browser(page, url):
    await page.goto(url)
    await page.wait_for_load_state('load')
    logger.info(f"Navigated to {url}")

@retry_async()
async def type_text(page, element_selector, text):
    selector = f"xpath={element_selector}"
    await page.wait_for_selector(selector, timeout=10000)
    await page.fill(selector, text)
    logger.info(f"Typed text '{text}' into element '{element_selector}'")

@retry_async()
async def click_element(page, element_selector):
    selector = f"xpath={element_selector}"
    await page.wait_for_selector(selector, timeout=10000)
    await page.click(selector)
    logger.info(f"Clicked element '{element_selector}'")

@retry_async()
async def dom_get_content_elements(page, query_text, max_results):
    # 搜索结果抓取逻辑
    # 这里我们假设搜索结果在页面上以文本形式出现
    # 实际情况需根据页面结构调整
    # 这里以百度搜索结果为例，通常结果在id="content_left"下
    # 但由于query_text不同，这里做通用处理
    results = []
    try:
        # 等待页面加载完成
        await page.wait_for_load_state('load')
        # 获取所有包含query_text的元素
        elements = await page.locator(f"text={query_text}").all()
        for el in elements[:max_results]:
            content = await el.text_content()
            results.append(content)
        logger.info(f"Found {len(results)} elements for query '{query_text}'")
    except PlaywrightTimeoutError:
        logger.warning(f"No elements found for query '{query_text}' within timeout.")
    return results

async def main():
    playwright = await async_playwright().start()
    browser = await playwright.chromium.launch(headless=False)
    page = await browser.new_page()
    try:
        # 1. 打开百度首页
        await navigate_browser(page, "https://www.baidu.com")

        # 2. 输入文本
        await type_text(
            page,
            "html/body/div[1]/div[1]/div[6]/div/div/div[2]/div/div/div[1]/div/div[1]/div[4]/div[1]/div[2]/textarea",
            "带带大师兄"
        )

        # 3. 点击搜索按钮
        await click_element(
            page,
            "html/body/div[3]/div[1]/div[6]/div/div/div[2]/div[2]/div/div/div/div/div[4]/div[1]/div[3]/div[3]/button"
        )

        # 4. 获取“搜索结果”相关内容
        results_1 = await dom_get_content_elements(page, "搜索结果", 20)
        logger.info(f"搜索结果: {results_1}")

        # 5. 获取“带带大师兄 搜索结果摘要”相关内容
        results_2 = await dom_get_content_elements(page, "带带大师兄 搜索结果摘要", 15)
        logger.info(f"带带大师兄 搜索结果摘要: {results_2}")

    except Exception as e:
        logger.error(f"Automation failed: {e}")
    finally:
        await browser.close()
        await playwright.stop()
        logger.info("Resources cleaned up.")

if __name__ == "__main__":
    asyncio.run(main())