import asyncio
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

MAX_RETRIES = 3

async def retry_async(func, *args, **kwargs):
    last_exc = None
    for attempt in range(1, MAX_RETRIES + 1):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            last_exc = e
            logger.warning(f"Attempt {attempt} failed with error: {e}")
            if attempt < MAX_RETRIES:
                await asyncio.sleep(1)
    logger.error(f"All {MAX_RETRIES} attempts failed.")
    raise last_exc

async def navigate_browser(page, url):
    async def _navigate():
        logger.info(f"Navigating to {url}")
        await page.goto(url)
        await page.wait_for_load_state('load')
    await retry_async(_navigate)

async def dom_get_content_elements(page, query_text, max_results=20, similarity_threshold=0.1):
    """
    查找页面中与query_text中任一关键词相似的文本元素，并打印其内容和XPath。
    """
    keywords = [kw.strip() for kw in query_text.split()]
    logger.info(f"Searching for keywords: {keywords}")

    async def _search():
        # 获取所有可见文本节点
        elements = await page.locator("xpath=//*").all()
        results = []
        for el in elements:
            try:
                text = (await el.text_content()) or ""
                text = text.strip()
                if not text:
                    continue
                for kw in keywords:
                    if kw in text:
                        # 获取XPath
                        xpath = await page.evaluate(
                            """el => {
                                function getXPath(element) {
                                    if (element.id !== '')
                                        return 'id(\"' + element.id + '\")';
                                    if (element === document.body)
                                        return '/html/body';
                                    var ix = 0;
                                    var siblings = element.parentNode.childNodes;
                                    for (var i=0; i<siblings.length; i++) {
                                        var sibling = siblings[i];
                                        if (sibling === element)
                                            return getXPath(element.parentNode) + '/' + element.tagName.toLowerCase() + '[' + (ix+1) + ']';
                                        if (sibling.nodeType === 1 && sibling.tagName === element.tagName)
                                            ix++;
                                    }
                                }
                                return getXPath(el);
                            }""",
                            el
                        )
                        results.append({'text': text, 'xpath': xpath})
                        break  # 一个元素只记录一次
                if len(results) >= max_results:
                    break
            except Exception as e:
                logger.debug(f"Error processing element: {e}")
        logger.info(f"Found {len(results)} elements matching keywords.")
        for idx, item in enumerate(results, 1):
            logger.info(f"{idx}. XPath: {item['xpath']}\n   Text: {item['text']}\n")
        return results

    return await retry_async(_search)

async def main():
    playwright = await async_playwright().start()
    browser = None
    page = None
    try:
        browser = await playwright.chromium.launch(headless=True)
        page = await browser.new_page()
        # Step 1: navigate_browser
        await navigate_browser(page, "https://about.meituan.com/")
        # Step 2: dom_get_content_elements
        await dom_get_content_elements(
            page,
            query_text="使命 愿景 企业文化 公司介绍",
            max_results=20,
            similarity_threshold=0.1
        )
    except Exception as e:
        logger.error(f"Script failed: {e}")
    finally:
        if page:
            await page.close()
        if browser:
            await browser.close()
        await playwright.stop()

if __name__ == "__main__":
    asyncio.run(main())